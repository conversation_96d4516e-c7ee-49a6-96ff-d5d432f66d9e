"use client";

import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { AdminStats } from '@/components/admin/AdminStats';
import { RevenueChart } from '@/components/admin/RevenueChart';
import { ChartCard } from '@/components/ui/ChartCard';
import { Button } from '@/components/ui/button';
import { useOrders } from '@/hooks/useOrders';
import { useMenu } from '@/hooks/useMenu';
import { PieChart, Pie, Cell, ResponsiveContainer } from 'recharts';
import { Calendar, Filter, TrendingUp, MoreHorizontal } from 'lucide-react';
import { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const COLORS = ['#22c55e', '#ef4444', '#eab308', '#8b5cf6'];

export default function AdminDashboard() {
  const { data: orders = [] } = useOrders();
  const { data: menus = [] } = useMenu();

  const pieData = useMemo(() => {
    const statusCounts = orders.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(statusCounts).map(([status, count]) => ({
      name: status.charAt(0).toUpperCase() + status.slice(1),
      value: count,
    }));
  }, [orders]);

  return (
    <DashboardLayout requiredRole="admin">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Admin Dashboard
            </h1>
            <p className="text-slate-600 mt-1">Monitor your restaurant's performance and analytics</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" className="border-slate-200 hover:bg-slate-50">
              <Calendar className="h-4 w-4 mr-2" />
              Last 7 days
            </Button>
            <Button variant="outline" className="border-slate-200 hover:bg-slate-50">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button className="bg-lupizza-gradient hover:shadow-lupizza text-white">
              <TrendingUp className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <AdminStats orders={orders} />

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Revenue Chart */}
          <RevenueChart orders={orders} className="lg:col-span-2" />

          {/* Order Status Chart */}
          <ChartCard title="Order Status Distribution">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${((percent ?? 0) * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
              </PieChart>
            </ResponsiveContainer>
          </ChartCard>
        </div>

        {/* Recent Orders */}
        <Card className="overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-white to-slate-50/50">
            <div className="flex items-center justify-between">
              <CardTitle className="text-slate-900">Recent Orders</CardTitle>
              <Button variant="outline" size="sm">
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y divide-slate-100">
              {orders.slice(0, 5).map((order, index) => (
                <div key={order.id} className="p-6 hover:bg-slate-50/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-lupizza-green-100 rounded-xl flex items-center justify-center">
                        <span className="text-sm font-semibold text-lupizza-green-600">
                          #{order.id.slice(-3)}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-slate-900">Order #{order.id.slice(-6)}</p>
                        <p className="text-sm text-slate-500">
                          {order.order_type} • {order.table_number || 'Takeaway'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-slate-900">
                        Rp {order.total.toLocaleString('id-ID')}
                      </p>
                      <div className="flex items-center justify-end mt-1">
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                          order.status === 'completed' 
                            ? 'bg-lupizza-green-100 text-lupizza-green-700'
                            : order.status === 'processing'
                            ? 'bg-blue-100 text-blue-700'
                            : 'bg-yellow-100 text-yellow-700'
                        }`}>
                          {order.status}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
