'use client';

import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { AdminStats } from '@/components/admin/AdminStats';
import { RevenueChart } from '@/components/admin/RevenueChart';
import { ChartCard } from '@/components/ui/ChartCard';
import { Button } from '@/components/ui/button';
import { DateRangePicker } from '@/components/ui/DateRangePicker';
import { useOrders } from '@/hooks/useOrders';
import { useMenu } from '@/hooks/useMenu';
import { useExport } from '@/hooks/useExport';
import { Pie<PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from 'recharts';
import {
  Calendar,
  Filter,
  TrendingUp,
  MoreHorizontal,
  Download,
  Eye,
} from 'lucide-react';
import { useMemo, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { DateRange } from 'react-day-picker';
import { addDays, subDays, format } from 'date-fns';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

const COLORS = ['#22c55e', '#ef4444', '#eab308', '#8b5cf6'];

export default function AdminDashboard() {
  const router = useRouter();
  const { data: orders = [] } = useOrders();
  const { data: menus = [] } = useMenu();
  const { exportReport, isExporting } = useExport();

  // State for date range and filters
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(new Date(), 7),
    to: new Date(),
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('7days');

  // Filter orders based on date range
  const filteredOrders = useMemo(() => {
    if (!dateRange.from) return orders;

    return orders.filter((order) => {
      const orderDate = new Date(order.created_at);
      return (
        (!dateRange.from || orderDate >= dateRange.from) &&
        (!dateRange.to || orderDate <= dateRange.to)
      );
    });
  }, [orders, dateRange]);

  // Generate pie chart data
  const pieData = useMemo(() => {
    const statusCounts = filteredOrders.reduce(
      (acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    return Object.entries(statusCounts).map(([status, count]) => ({
      name: status.charAt(0).toUpperCase() + status.slice(1),
      value: count,
    }));
  }, [filteredOrders]);

  // Handle date range selection
  const handleDateRangeChange = (range: DateRange) => {
    setDateRange(range);
  };

  // Handle preset period selection
  const handlePeriodSelect = (period: string) => {
    const today = new Date();
    let from = today;

    switch (period) {
      case '7days':
        from = subDays(today, 7);
        break;
      case '30days':
        from = subDays(today, 30);
        break;
      case '90days':
        from = subDays(today, 90);
        break;
      case 'year':
        from = new Date(today.getFullYear(), 0, 1); // Jan 1st of current year
        break;
    }

    setDateRange({ from, to: today });
    setSelectedPeriod(period);
  };

  // Handle export
  const handleExport = async (format: 'csv' | 'excel' | 'pdf') => {
    try {
      await exportReport({
        type: 'sales',
        format,
        dateRange: {
          from: dateRange.from || new Date(),
          to: dateRange.to || new Date(),
        },
        data: {
          sales: {
            orders: filteredOrders,
            totalRevenue: filteredOrders.reduce(
              (sum, order) => sum + order.total,
              0
            ),
            dailyBreakdown: filteredOrders,
          },
        },
      });
      toast.success(`Report exported as ${format.toUpperCase()}`);
    } catch (error) {
      toast.error('Failed to export report');
      console.error(error);
    }
  };

  // Navigate to orders page
  const handleViewAllOrders = () => {
    router.push('/dashboard/admin/orders');
  };

  return (
    <DashboardLayout requiredRole="admin">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Admin Dashboard
            </h1>
            <p className="text-slate-600 mt-1">
              Monitor your restaurant's performance and analytics
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {/* Date Range Picker */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="border-slate-200 hover:bg-slate-50"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  {selectedPeriod === '7days' && 'Last 7 days'}
                  {selectedPeriod === '30days' && 'Last 30 days'}
                  {selectedPeriod === '90days' && 'Last 90 days'}
                  {selectedPeriod === 'year' && 'This year'}
                  {selectedPeriod === 'custom' && 'Custom range'}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => handlePeriodSelect('7days')}>
                  Last 7 days
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handlePeriodSelect('30days')}>
                  Last 30 days
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handlePeriodSelect('90days')}>
                  Last 90 days
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handlePeriodSelect('year')}>
                  This year
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Filter Dialog */}
            <Dialog open={isFilterOpen} onOpenChange={setIsFilterOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="border-slate-200 hover:bg-slate-50"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Filter Dashboard Data</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Date Range
                    </label>
                    <DateRangePicker
                      value={dateRange}
                      onChange={handleDateRangeChange}
                      placeholder="Select date range"
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsFilterOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={() => {
                        setSelectedPeriod('custom');
                        setIsFilterOpen(false);
                        toast.success('Filter applied successfully');
                      }}
                    >
                      Apply Filter
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Cards */}
        <AdminStats orders={filteredOrders} />

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Revenue Chart */}
          <RevenueChart orders={filteredOrders} className="lg:col-span-2" />

          {/* Order Status Chart */}
          <ChartCard title="Order Status Distribution">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) =>
                    `${name} ${((percent ?? 0) * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
              </PieChart>
            </ResponsiveContainer>
          </ChartCard>
        </div>

        {/* Recent Orders */}
        <Card className="overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-white to-slate-50/50">
            <div className="flex items-center justify-between">
              <CardTitle className="text-slate-900">Recent Orders</CardTitle>
              <Button variant="outline" size="sm" onClick={handleViewAllOrders}>
                <Eye className="h-4 w-4 mr-1" />
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y divide-slate-100">
              {filteredOrders.slice(0, 5).map((order, index) => (
                <div
                  key={order.id}
                  className="p-6 hover:bg-slate-50/50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-lupizza-green-100 rounded-xl flex items-center justify-center">
                        <span className="text-sm font-semibold text-lupizza-green-600">
                          #{order.id.slice(-3)}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-slate-900">
                          Order #{order.id.slice(-6)}
                        </p>
                        <p className="text-sm text-slate-500">
                          {order.order_type} •{' '}
                          {order.table_number || 'Takeaway'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-slate-900">
                        Rp {order.total.toLocaleString('id-ID')}
                      </p>
                      <div className="flex items-center justify-end mt-1">
                        <div
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            order.status === 'completed'
                              ? 'bg-lupizza-green-100 text-lupizza-green-700'
                              : order.status === 'processing'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-yellow-100 text-yellow-700'
                          }`}
                        >
                          {order.status}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
