'use client';

import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { AdminStats } from '@/components/admin/AdminStats';
import { ModernRevenueChart } from '@/components/admin/ModernRevenueChart';
import { OrderStatusChart } from '@/components/admin/OrderStatusChart';
import { DashboardHeader } from '@/components/ui/DashboardHeader';
import { DashboardFilters } from '@/components/ui/DashboardFilters';
import {
  DashboardContainer,
  DashboardSection,
  DashboardGrid,
} from '@/components/ui/DashboardContainer';
import { RecentOrdersList } from '@/components/ui/RecentOrdersList';
import { useOrders } from '@/hooks/useOrders';
import { useMenu } from '@/hooks/useMenu';
import { useExport } from '@/hooks/useExport';
import { useDashboardFilters } from '@/hooks/useDashboardFilters';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

export default function AdminDashboard() {
  const router = useRouter();
  const { data: orders = [] } = useOrders();
  const { data: menus = [] } = useMenu();
  const { exportReport, isExporting } = useExport();

  // Use dashboard filters hook
  const {
    dateRange,
    isFilterOpen,
    selectedPeriod,
    filteredOrders,
    filterSummary,
    handleDateRangeChange,
    handlePeriodSelect,
    setIsFilterOpen,
  } = useDashboardFilters(orders);

  // Handle export
  const handleExport = async (format: 'csv' | 'excel' | 'pdf') => {
    try {
      await exportReport({
        type: 'sales',
        format,
        dateRange: {
          from: dateRange.from || new Date(),
          to: dateRange.to || new Date(),
        },
        data: {
          sales: {
            orders: filteredOrders,
            totalRevenue: filteredOrders.reduce(
              (sum, order) => sum + order.total,
              0
            ),
            dailyBreakdown: filteredOrders,
          },
        },
      });
      toast.success(`Report exported as ${format.toUpperCase()}`);
    } catch (error) {
      toast.error('Failed to export report');
      console.error(error);
    }
  };

  // Navigate to orders page
  const handleViewAllOrders = () => {
    router.push('/dashboard/admin/orders');
  };

  return (
    <DashboardLayout requiredRole="admin">
      <DashboardContainer>
        {/* Header with Filters */}
        <DashboardHeader
          title="Admin Dashboard"
          subtitle={`Monitor your restaurant's performance and analytics ${
            filterSummary.isFiltered
              ? `(${filterSummary.filtered} of ${filterSummary.total} orders)`
              : ''
          }`}
        >
          <DashboardFilters
            dateRange={dateRange}
            onDateRangeChange={handleDateRangeChange}
            selectedPeriod={selectedPeriod}
            onPeriodSelect={handlePeriodSelect}
            isFilterOpen={isFilterOpen}
            onFilterOpenChange={setIsFilterOpen}
            onExport={handleExport}
            isExporting={isExporting}
          />
        </DashboardHeader>

        {/* Stats Section */}
        <DashboardSection title="Overview" subtitle="Key performance metrics">
          <AdminStats orders={filteredOrders} />
        </DashboardSection>

        {/* Charts Section */}
        <DashboardSection title="Analytics" subtitle="Revenue and order trends">
          <DashboardGrid columns={3}>
            <div className="lg:col-span-2">
              <ModernRevenueChart orders={filteredOrders} />
            </div>
            <div className="lg:col-span-1">
              <OrderStatusChart orders={filteredOrders} />
            </div>
          </DashboardGrid>
        </DashboardSection>

        {/* Recent Orders Section */}
        <DashboardSection
          title="Recent Activity"
          subtitle="Latest orders and transactions"
        >
          <RecentOrdersList
            orders={filteredOrders}
            onViewAll={handleViewAllOrders}
            maxItems={5}
            title="Recent Orders"
            showViewAll={true}
          />
        </DashboardSection>
      </DashboardContainer>
    </DashboardLayout>
  );
}
