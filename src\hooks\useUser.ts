"use client";

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { authService } from '@/services/authService';
import { Profile } from '@/types';
import { useEffect } from 'react';
import { supabase } from '@/lib/supabase';

export const useUser = () => {
  const queryClient = useQueryClient();

  // Listen to auth state changes
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_OUT') {
          // Clear all queries when user signs out
          queryClient.clear();
        } else if (event === 'SIGNED_IN' && session) {
          // Invalidate user query to refetch fresh data
          queryClient.invalidateQueries({ queryKey: ['user'] });
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [queryClient]);

  return useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      const user = await authService.getCurrentUser();
      if (!user) return null;
      
      const profile = await authService.getUserProfile(user.id);
      return profile;
    },
    retry: false,
    staleTime: 0, // Always refetch to ensure fresh data
    gcTime: 0, // Don't cache after component unmount
  });
};

export const useUserProfile = (userId: string) => {
  return useQuery({
    queryKey: ['userProfile', userId],
    queryFn: () => authService.getUserProfile(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
