'use client';

import * as React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { StaticDatePicker } from '@mui/x-date-pickers/StaticDatePicker';
import { Button } from '@/components/ui/button';
import { DateRange as ReactDayPickerDateRange } from 'react-day-picker';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface MaterialDateRangePickerProps {
  value: ReactDayPickerDateRange;
  onChange: (range: ReactDayPickerDateRange) => void;
  onClose?: () => void;
}

interface ShortcutItem {
  label: string;
  getValue: () => [Dayjs, Dayjs] | [null, null];
}

const shortcutsItems: ShortcutItem[] = [
  {
    label: 'Last 7 Days',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(6, 'day'), today];
    },
  },
  {
    label: 'Last 30 Days',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(29, 'day'), today];
    },
  },
  {
    label: 'Last 90 Days',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(89, 'day'), today];
    },
  },
  {
    label: 'This Week',
    getValue: () => {
      const today = dayjs();
      return [today.startOf('week'), today.endOf('week')];
    },
  },
  {
    label: 'This Month',
    getValue: () => {
      const today = dayjs();
      return [today.startOf('month'), today.endOf('month')];
    },
  },
  {
    label: 'This Year',
    getValue: () => {
      const today = dayjs();
      return [today.startOf('year'), today.endOf('year')];
    },
  },
  {
    label: 'Reset',
    getValue: () => [null, null],
  },
];

export function MaterialDateRangePicker({
  value,
  onChange,
  onClose
}: MaterialDateRangePickerProps) {
  const [fromDate, setFromDate] = React.useState<Dayjs | null>(
    value.from ? dayjs(value.from) : null
  );
  const [toDate, setToDate] = React.useState<Dayjs | null>(
    value.to ? dayjs(value.to) : null
  );
  const [selectingFrom, setSelectingFrom] = React.useState(true);

  // Update local state when props change
  React.useEffect(() => {
    setFromDate(value.from ? dayjs(value.from) : null);
    setToDate(value.to ? dayjs(value.to) : null);
  }, [value.from, value.to]);

  const handleDateChange = (newDate: Dayjs | null) => {
    if (selectingFrom) {
      setFromDate(newDate);
      setSelectingFrom(false);
    } else {
      setToDate(newDate);
      setSelectingFrom(true);

      // Apply the range
      const reactDayPickerRange: ReactDayPickerDateRange = {
        from: fromDate ? fromDate.toDate() : undefined,
        to: newDate ? newDate.toDate() : undefined,
      };

      onChange(reactDayPickerRange);

      // Auto close when both dates are selected
      if (fromDate && newDate && onClose) {
        setTimeout(() => {
          onClose();
        }, 300);
      }
    }
  };

  const handleShortcut = (shortcut: ShortcutItem) => {
    const [from, to] = shortcut.getValue();

    if (from === null && to === null) {
      setFromDate(null);
      setToDate(null);
      onChange({ from: undefined, to: undefined });
    } else {
      setFromDate(from);
      setToDate(to);
      onChange({
        from: from ? from.toDate() : undefined,
        to: to ? to.toDate() : undefined,
      });
    }

    if (onClose) {
      setTimeout(() => {
        onClose();
      }, 300);
    }
  };

  const getDateRangeText = () => {
    if (fromDate && toDate) {
      return `${fromDate.format('MMM DD')} - ${toDate.format('MMM DD')}`;
    } else if (fromDate) {
      return `From: ${fromDate.format('MMM DD')}`;
    } else if (toDate) {
      return `To: ${toDate.format('MMM DD')}`;
    }
    return 'Select date range';
  };

  return (
    <div className="bg-white rounded-lg shadow-lg border border-slate-200 overflow-hidden">
      <div className="flex">
        {/* Shortcuts */}
        <div className="w-40 bg-slate-50 border-r border-slate-200 p-3">
          <div className="space-y-1">
            {shortcutsItems.map((shortcut) => (
              <Button
                key={shortcut.label}
                variant="ghost"
                size="sm"
                className="w-full justify-start text-xs h-8 font-normal hover:bg-slate-100"
                onClick={() => handleShortcut(shortcut)}
              >
                {shortcut.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Date Picker */}
        <div className="flex-1">
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <div className="p-3 border-b border-slate-200 bg-slate-50">
              <div className="text-sm font-medium text-slate-700 mb-1">
                {selectingFrom ? 'Select start date' : 'Select end date'}
              </div>
              <div className="text-xs text-slate-500">
                {getDateRangeText()}
              </div>
            </div>
            <StaticDatePicker
              value={selectingFrom ? fromDate : toDate}
              onChange={handleDateChange}
              sx={{
                '& .MuiPickersLayout-root': {
                  maxWidth: '320px',
                  width: '100%',
                },
                '& .MuiPickersCalendarHeader-root': {
                  paddingLeft: 1,
                  paddingRight: 1,
                },
                '& .MuiDayCalendar-root': {
                  width: '100%',
                  maxWidth: '320px',
                },
                '& .MuiPickersDay-root': {
                  fontSize: '0.875rem',
                  width: '36px',
                  height: '36px',
                },
                '& .MuiPaper-root': {
                  boxShadow: 'none',
                  backgroundColor: 'transparent',
                },
                '& .MuiPickersLayout-actionBar': {
                  display: 'none',
                },
              }}
            />
          </LocalizationProvider>
        </div>
      </div>

      {/* Action Bar */}
      <div className="p-3 border-t border-slate-200 bg-slate-50 flex justify-between items-center">
        <div className="text-xs text-slate-500">
          {fromDate && toDate ? 'Range selected' : selectingFrom ? 'Select start date' : 'Select end date'}
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setFromDate(null);
              setToDate(null);
              setSelectingFrom(true);
              onChange({ from: undefined, to: undefined });
            }}
          >
            Clear
          </Button>
          <Button
            size="sm"
            onClick={onClose}
            disabled={!fromDate || !toDate}
          >
            Apply
          </Button>
        </div>
      </div>
    </div>
  );
};
