"use client";

import * as React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { StaticDateRangePicker } from '@mui/x-date-pickers-pro/StaticDateRangePicker';
import { PickersShortcutsItem } from '@mui/x-date-pickers/PickersShortcuts';
import { DateRange } from '@mui/x-date-pickers-pro/models';
import { DateRange as ReactDayPickerDateRange } from 'react-day-picker';

interface MaterialDateRangePickerProps {
  value: ReactDayPickerDateRange;
  onChange: (range: ReactDayPickerDateRange) => void;
  onClose?: () => void;
}

const shortcutsItems: PickersShortcutsItem<DateRange<Dayjs>>[] = [
  {
    label: 'Last 7 Days',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(6, 'day'), today];
    },
  },
  {
    label: 'Last 30 Days',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(29, 'day'), today];
    },
  },
  {
    label: 'Last 90 Days',
    getValue: () => {
      const today = dayjs();
      return [today.subtract(89, 'day'), today];
    },
  },
  {
    label: 'This Week',
    getValue: () => {
      const today = dayjs();
      return [today.startOf('week'), today.endOf('week')];
    },
  },
  {
    label: 'This Month',
    getValue: () => {
      const today = dayjs();
      return [today.startOf('month'), today.endOf('month')];
    },
  },
  {
    label: 'This Year',
    getValue: () => {
      const today = dayjs();
      return [today.startOf('year'), today.endOf('year')];
    },
  },
  { 
    label: 'Reset', 
    getValue: () => [null, null] 
  },
];

export function MaterialDateRangePicker({ 
  value, 
  onChange, 
  onClose 
}: MaterialDateRangePickerProps) {
  // Convert react-day-picker DateRange to dayjs DateRange
  const dayjsValue: DateRange<Dayjs> = React.useMemo(() => [
    value.from ? dayjs(value.from) : null,
    value.to ? dayjs(value.to) : null,
  ], [value.from, value.to]);

  const handleChange = (newValue: DateRange<Dayjs>) => {
    const [from, to] = newValue;
    
    // Convert dayjs DateRange back to react-day-picker DateRange
    const reactDayPickerRange: ReactDayPickerDateRange = {
      from: from ? from.toDate() : undefined,
      to: to ? to.toDate() : undefined,
    };
    
    onChange(reactDayPickerRange);
    
    // Auto close when both dates are selected
    if (from && to && onClose) {
      setTimeout(() => {
        onClose();
      }, 300); // Small delay for better UX
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg border border-slate-200 overflow-hidden">
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <StaticDateRangePicker
          value={dayjsValue}
          onChange={handleChange}
          slotProps={{
            shortcuts: {
              items: shortcutsItems,
            },
            actionBar: {
              actions: ['clear', 'cancel', 'accept'],
            },
          }}
          sx={{
            '& .MuiPickersLayout-root': {
              maxWidth: '100%',
              width: '100%',
            },
            '& .MuiDateRangeCalendar-root': {
              maxWidth: '100%',
              width: '100%',
            },
            '& .MuiPickersCalendarHeader-root': {
              paddingLeft: 1,
              paddingRight: 1,
            },
            '& .MuiDayCalendar-root': {
              width: '100%',
              maxWidth: '320px',
            },
            '& .MuiPickersDay-root': {
              fontSize: '0.875rem',
              width: '36px',
              height: '36px',
            },
            '& .MuiPickersShortcuts-root': {
              width: '140px',
              '& .MuiChip-root': {
                fontSize: '0.75rem',
                height: '28px',
              },
            },
            '& .MuiPickersLayout-contentWrapper': {
              display: 'flex',
              flexDirection: 'row',
            },
            // Custom styling for better integration
            '& .MuiPaper-root': {
              boxShadow: 'none',
              backgroundColor: 'transparent',
            },
            '& .MuiPickersLayout-actionBar': {
              padding: '12px 16px',
              borderTop: '1px solid #e2e8f0',
              backgroundColor: '#f8fafc',
            },
          }}
        />
      </LocalizationProvider>
    </div>
  );
}
