import { supabase } from '@/lib/supabase';
import { Menu } from '@/types';

export const menuService = {
  async getMenus(): Promise<Menu[]> {
    const { data, error } = await supabase
      .from('menus')
      .select('*')
      .order('category', { ascending: true })
      .order('name', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async getMenusByCategory(category: string): Promise<Menu[]> {
    const { data, error } = await supabase
      .from('menus')
      .select('*')
      .eq('category', category)
      .eq('available', true)
      .order('name', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async createMenu(menu: Omit<Menu, 'id' | 'created_at' | 'updated_at'>): Promise<Menu> {
    const { data, error } = await supabase
      .from('menus')
      .insert(menu)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async updateMenu(id: string, updates: Partial<Menu>): Promise<Menu> {
    const { data, error } = await supabase
      .from('menus')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async deleteMenu(id: string): Promise<void> {
    const { error } = await supabase
      .from('menus')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  },

  async getCategories(): Promise<string[]> {
    const { data, error } = await supabase
      .from('menus')
      .select('category')
      .order('category', { ascending: true });
    
    if (error) throw error;
    
    const categories = Array.from(new Set(data?.map(item => item.category) || []));
    return categories;
  },
};