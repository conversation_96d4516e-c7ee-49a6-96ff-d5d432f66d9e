@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 142 71% 45%;  /* LuPizza Green */
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 142 71% 45%;  /* LuPizza Green */
  --radius: 1rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 142 71% 45%;  /* LuPizza Green */
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 142 71% 45%;  /* LuPizza Green */
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* LuPizza Brand Gradients */
.bg-lupizza-gradient {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.bg-lupizza-gradient-red {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.bg-lupizza-gradient-warm {
  background: linear-gradient(135deg, #22c55e 0%, #eab308 50%, #ef4444 100%);
}

/* Modern glassmorphism effects with LuPizza colors */
.glass-lupizza {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.glass-lupizza-red {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Smooth animations */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.animate-glow-lupizza {
  animation: glow-lupizza 2s ease-in-out infinite alternate;
}

@keyframes glow-lupizza {
  from { box-shadow: 0 0 20px rgba(34, 197, 94, 0.4); }
  to { box-shadow: 0 0 30px rgba(34, 197, 94, 0.6); }
}

/* Custom scrollbar with LuPizza colors */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #22c55e, #16a34a);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #16a34a, #15803d);
}

/* LuPizza Button Styles */
.btn-lupizza-primary {
  @apply bg-lupizza-green-600 hover:bg-lupizza-green-700 text-white font-medium px-4 py-2 rounded-md transition-all duration-200 shadow-lupizza;
}

.btn-lupizza-secondary {
  @apply bg-lupizza-red-500 hover:bg-lupizza-red-600 text-white font-medium px-4 py-2 rounded-md transition-all duration-200 shadow-lupizza-red;
}

.btn-lupizza-outline {
  @apply border-2 border-lupizza-green-500 text-lupizza-green-600 hover:bg-lupizza-green-500 hover:text-white font-medium px-4 py-2 rounded-md transition-all duration-200;
}

/* Form styles with LuPizza branding */
.form-input-lupizza {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-lupizza-green-500 focus:border-transparent transition-all duration-200;
}

/* Card styles with LuPizza theme */
.card-lupizza {
  @apply bg-white rounded-lg shadow-lupizza border border-lupizza-green-100 p-6;
}

/* Loading states with LuPizza colors */
.loading-shimmer-lupizza {
  background: linear-gradient(90deg, #dcfce7 25%, #bbf7d0 50%, #dcfce7 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Pizza themed decorative elements */
.pizza-pattern {
  background-image: radial-gradient(circle at 25% 25%, #22c55e 2px, transparent 2px),
                    radial-gradient(circle at 75% 75%, #ef4444 2px, transparent 2px);
  background-size: 50px 50px;
  opacity: 0.1;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .mobile-stack {
    @apply flex-col space-y-4;
  }
  
  .mobile-full {
    @apply w-full;
  }
}

/* Print styles for receipts */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}

