"use client";

import { useState } from 'react';
import { useUser } from '@/hooks/useUser';
import { authService } from '@/services/authService';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { 
  Pizza, 
  LogOut, 
  User, 
  Settings, 
  Bell, 
  Search,
  Menu,
  Sparkles
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import { useQueryClient } from '@tanstack/react-query';

export function Navbar() {
  const { data: user } = useUser();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      // Clear all cached data
      queryClient.clear();
      
      // Sign out
      await authService.signOut();
      
      // The authService.signOut() already handles redirect
      toast.success('Logged out successfully');
    } catch (error) {
      toast.error('Failed to logout');
      setIsLoading(false);
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-lupizza-red-100 text-lupizza-red-700 border-lupizza-red-200';
      case 'cashier':
        return 'bg-lupizza-green-100 text-lupizza-green-700 border-lupizza-green-200';
      case 'kitchen':
        return 'bg-lupizza-cream-100 text-lupizza-cream-700 border-lupizza-cream-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <nav className="h-16 bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-sm sticky top-0 z-50">
      <div className="h-full px-6 flex items-center justify-between">
        {/* Logo & Brand */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-lupizza-gradient rounded-xl blur-sm opacity-30"></div>
              <div className="relative bg-lupizza-gradient w-10 h-10 rounded-xl flex items-center justify-center shadow-lupizza">
                <Pizza className="h-6 w-6 text-white" />
              </div>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-800">
                LuPizza
              </h1>
              <p className="text-xs text-slate-500 -mt-1">Point of Sale</p>
            </div>
          </div>
        </div>

        {/* Search Bar - Hidden on mobile */}
        <div className="hidden md:flex flex-1 max-w-md mx-8">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <input
              type="text"
              placeholder="Search orders, menu items..."
              className="w-full pl-10 pr-4 py-2 bg-slate-50/50 border border-slate-200/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-lupizza-green-500/20 focus:border-lupizza-green-300 transition-all duration-200"
            />
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <Button variant="ghost" size="sm" className="relative p-2 hover:bg-slate-100/50">
            <Bell className="h-5 w-5 text-slate-600" />
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-lupizza-red-500 rounded-full flex items-center justify-center">
              <span className="text-[10px] text-white font-bold">3</span>
            </span>
          </Button>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-3 p-2 hover:bg-slate-100/50 rounded-xl">
                <div className="flex items-center space-x-3">
                  <div className="text-right hidden sm:block">
                    <p className="text-sm font-medium text-slate-700">
                      {user?.email?.split('@')[0] || 'User'}
                    </p>
                    <div className="flex items-center justify-end space-x-1">
                      <Badge variant="outline" className={`text-xs px-2 py-0.5 ${getRoleBadgeColor(user?.role || '')}`}>
                        <Sparkles className="h-3 w-3 mr-1" />
                        {(user?.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1) : 'User')}
                      </Badge>
                    </div>
                  </div>
                  <Avatar className="h-9 w-9 ring-2 ring-lupizza-green-100">
                    <AvatarImage src={undefined} />
                    <AvatarFallback className="bg-lupizza-gradient text-white font-semibold">
                      {user?.email?.charAt(0).toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 bg-white/95 backdrop-blur-xl border-white/20 shadow-xl">
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">{user?.email?.split('@')[0]}</p>
                  <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-white/20" />
              <DropdownMenuItem className="hover:bg-slate-50/50">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-slate-50/50">
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-white/20" />
              <DropdownMenuItem 
                onClick={handleLogout}
                disabled={isLoading}
                className="hover:bg-lupizza-red-50 text-lupizza-red-600"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>{isLoading ? 'Logging out...' : 'Log out'}</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </nav>
  );
}
