"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Filter, X, Users } from 'lucide-react';

interface UserFiltersProps {
  selectedRole: string;
  onRoleChange: (role: string) => void;
}

export function UserFilters({ selectedRole, onRoleChange }: UserFiltersProps) {
  const clearFilters = () => {
    onRoleChange('all');
  };

  const hasActiveFilters = selectedRole !== 'all';

  const roles = [
    { value: 'admin', label: 'Admin', color: 'bg-red-100 text-red-800' },
    { value: 'cashier', label: 'Cashier', color: 'bg-green-100 text-green-800' },
    { value: 'kitchen', label: 'Kitchen', color: 'bg-blue-100 text-blue-800' },
  ];

  return (
    <div className="flex items-center space-x-3">
      <div className="flex items-center space-x-2">
        <Filter className="h-4 w-4 text-slate-500" />
        <span className="text-sm font-medium text-slate-700">Filters:</span>
      </div>
      
      <Select value={selectedRole} onValueChange={onRoleChange}>
        <SelectTrigger className="w-40">
          <SelectValue placeholder="Role" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2" />
              All Roles
            </div>
          </SelectItem>
          {roles.map((role) => (
            <SelectItem key={role.value} value={role.value}>
              <div className="flex items-center">
                <div className={`w-2 h-2 rounded-full mr-2 ${role.color.split(' ')[0]}`}></div>
                {role.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {hasActiveFilters && (
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="flex items-center space-x-1">
            <span>Role: {selectedRole}</span>
            <button onClick={clearFilters} className="ml-1">
              <X className="h-3 w-3" />
            </button>
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-slate-600 hover:text-slate-900"
          >
            Clear all
          </Button>
        </div>
      )}
    </div>
  );
}