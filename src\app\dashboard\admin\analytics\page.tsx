"use client";

import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { AnalyticsOverview } from '@/components/admin/analytics/AnalyticsOverview';
import { RevenueChart } from '@/components/admin/analytics/RevenueChart';
import { TopMenuItems } from '@/components/admin/analytics/TopMenuItems';
import { SalesComparison } from '@/components/admin/analytics/SalesComparison';
import { CustomerInsights } from '@/components/admin/analytics/CustomerInsights';
import { DateRangePicker } from '@/components/ui/DateRangePicker';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useOrders } from '@/hooks/useOrders';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useState } from 'react';
import { subDays } from 'date-fns';

export default function AnalyticsPage() {
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to?: Date | undefined }>({
    from: subDays(new Date(), 30),
    to: new Date()
  });

  const { data: orders = [] } = useOrders();
  const { data: analytics } = useAnalytics({
    from: dateRange.from ?? new Date(),
    to: dateRange.to ?? new Date()
  });

  return (
    <DashboardLayout requiredRole="admin">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Analytics Dashboard</h1>
            <p className="text-slate-600 mt-1">Comprehensive business insights and performance metrics</p>
          </div>
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
          />
        </div>

        {/* Overview Stats */}
        <AnalyticsOverview analytics={analytics} />

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <RevenueChart
            orders={orders}
            dateRange={{
              from: dateRange.from ?? new Date(),
              to: dateRange.to ?? new Date()
            }}
          />
          <SalesComparison
            orders={orders}
            dateRange={{
              from: dateRange.from ?? new Date(),
              to: dateRange.to ?? new Date()
            }}
          />
        </div>

        {/* Insights Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <TopMenuItems orders={orders} />
          <CustomerInsights orders={orders} />
        </div>
      </div>
    </DashboardLayout>
  );
}