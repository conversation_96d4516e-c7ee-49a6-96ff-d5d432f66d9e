'use client';

import * as React from 'react';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface MinimalDatePickerProps {
  dateRange: DateRange;
  onDateRangeChange: (range: DateRange) => void;
  selectedPeriod: string;
  onPeriodSelect: (period: string) => void;
  className?: string;
}

export function MinimalDatePicker({
  dateRange,
  onDateRangeChange,
  selectedPeriod,
  onPeriodSelect,
  className,
}: MinimalDatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  const formatDateRange = () => {
    if (selectedPeriod === 'custom' && dateRange.from && dateRange.to) {
      // Check if same month and year
      if (
        dateRange.from.getMonth() === dateRange.to.getMonth() &&
        dateRange.from.getFullYear() === dateRange.to.getFullYear()
      ) {
        // Same month: "Dec 15-22"
        const month = format(dateRange.from, 'MMM');
        const fromDay = format(dateRange.from, 'd');
        const toDay = format(dateRange.to, 'd');
        return `${month} ${fromDay}-${toDay}`;
      } else {
        // Different months: "Dec 15 - Jan 22"
        const fromDate = format(dateRange.from, 'MMM d');
        const toDate = format(dateRange.to, 'MMM d');
        return `${fromDate} - ${toDate}`;
      }
    }

    switch (selectedPeriod) {
      case '7days':
        return 'Last 7 days';
      case '30days':
        return 'Last 30 days';
      case '90days':
        return 'Last 90 days';
      case 'year':
        return 'This year';
      default:
        return 'Last 7 days';
    }
  };

  const handleDateSelect = (range: DateRange | undefined) => {
    if (range) {
      onDateRangeChange(range);
      onPeriodSelect('custom');
      if (range.from && range.to) {
        setIsOpen(false);
      }
    }
  };

  const handlePresetSelect = (period: string) => {
    onPeriodSelect(period);
    setIsOpen(false);
  };

  return (
    <div className={cn('grid gap-2', className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            size="sm"
            className={cn(
              'justify-start text-left font-medium text-slate-700 border-slate-200 hover:bg-slate-50',
              !dateRange && 'text-muted-foreground'
            )}
          >
            <CalendarIcon className="mr-1.5 h-3.5 w-3.5" />
            {formatDateRange()}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-80 p-0 bg-white border border-slate-200"
          align="start"
        >
          <div className="p-3 border-b border-slate-100">
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant={selectedPeriod === '7days' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handlePresetSelect('7days')}
                className="text-xs"
              >
                7 days
              </Button>
              <Button
                variant={selectedPeriod === '30days' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handlePresetSelect('30days')}
                className="text-xs"
              >
                30 days
              </Button>
              <Button
                variant={selectedPeriod === '90days' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handlePresetSelect('90days')}
                className="text-xs"
              >
                90 days
              </Button>
              <Button
                variant={selectedPeriod === 'year' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handlePresetSelect('year')}
                className="text-xs"
              >
                This year
              </Button>
            </div>
          </div>
          <div className="p-3">
            <Calendar
              initialFocus
              mode="range"
              defaultMonth={dateRange?.from}
              selected={dateRange}
              onSelect={handleDateSelect}
              numberOfMonths={1}
              className="rounded-md border-0 w-full"
              classNames={{
                months:
                  'flex w-full flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',
                month: 'space-y-4 w-full',
                caption: 'flex justify-center pt-1 relative items-center',
                caption_label: 'text-sm font-medium',
                nav: 'space-x-1 flex items-center',
                nav_button:
                  'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100',
                nav_button_previous: 'absolute left-1',
                nav_button_next: 'absolute right-1',
                table: 'w-full border-collapse space-y-1',
                head_row: 'flex w-full',
                head_cell:
                  'text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] flex-1 text-center',
                row: 'flex w-full mt-2',
                cell: 'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 flex-1',
                day: 'h-8 w-8 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-md mx-auto',
                day_range_start: 'day-range-start',
                day_range_end: 'day-range-end',
                day_selected:
                  'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
                day_today: 'bg-accent text-accent-foreground',
                day_outside: 'text-muted-foreground opacity-50',
                day_disabled: 'text-muted-foreground opacity-50',
                day_range_middle:
                  'aria-selected:bg-accent aria-selected:text-accent-foreground',
                day_hidden: 'invisible',
              }}
            />
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
