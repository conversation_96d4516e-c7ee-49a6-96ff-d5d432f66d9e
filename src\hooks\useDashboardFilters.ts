"use client";

import { useState, useMemo } from 'react';
import { DateRange } from 'react-day-picker';
import { subDays } from 'date-fns';
import { Order } from '@/types';

export function useDashboardFilters(orders: Order[]) {
  // State for date range and filters
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(new Date(), 7),
    to: new Date(),
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('7days');

  // Filter orders based on date range
  const filteredOrders = useMemo(() => {
    if (!dateRange.from) return orders;
    
    return orders.filter(order => {
      const orderDate = new Date(order.created_at);
      return (
        (!dateRange.from || orderDate >= dateRange.from) && 
        (!dateRange.to || orderDate <= dateRange.to)
      );
    });
  }, [orders, dateRange]);

  // Handle date range selection
  const handleDateRangeChange = (range: DateRange) => {
    setDateRange(range);
  };
  
  // Handle preset period selection
  const handlePeriodSelect = (period: string) => {
    const today = new Date();
    let from = today;
    
    switch (period) {
      case '7days':
        from = subDays(today, 7);
        break;
      case '30days':
        from = subDays(today, 30);
        break;
      case '90days':
        from = subDays(today, 90);
        break;
      case 'year':
        from = new Date(today.getFullYear(), 0, 1); // Jan 1st of current year
        break;
    }
    
    setDateRange({ from, to: today });
    setSelectedPeriod(period);
  };

  // Get filter summary
  const filterSummary = useMemo(() => {
    const total = orders.length;
    const filtered = filteredOrders.length;
    const percentage = total > 0 ? Math.round((filtered / total) * 100) : 0;
    
    return {
      total,
      filtered,
      percentage,
      isFiltered: filtered !== total
    };
  }, [orders.length, filteredOrders.length]);

  return {
    // State
    dateRange,
    isFilterOpen,
    selectedPeriod,
    
    // Data
    filteredOrders,
    filterSummary,
    
    // Handlers
    handleDateRangeChange,
    handlePeriodSelect,
    setIsFilterOpen,
    setSelectedPeriod,
  };
}
