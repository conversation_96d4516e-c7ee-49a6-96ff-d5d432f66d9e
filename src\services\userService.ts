import { supabase } from '@/lib/supabase';
import { Profile } from '@/types';

export const userService = {
  async getUsers(): Promise<Profile[]> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getUserById(id: string): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  },

  async createUser(userData: {
    full_name: string;
    email: string;
    password: string;
    role: 'admin' | 'cashier' | 'kitchen';
    phone?: string;
    is_active?: boolean;
    avatar_file?: File | null;
  }): Promise<Profile> {
    try {
      // First create the auth user
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true,
        user_metadata: {
          full_name: userData.full_name,
          role: userData.role,
        }
      });

      if (authError) throw authError;

      // Handle avatar upload if provided
      let avatarUrl = null;
      if (userData.avatar_file) {
        const fileExt = userData.avatar_file.name.split('.').pop();
        const fileName = `${authData.user.id}.${fileExt}`;
        
        const { error: uploadError } = await supabase.storage
          .from('avatars')
          .upload(fileName, userData.avatar_file);

        if (!uploadError) {
          const { data: { publicUrl } } = supabase.storage
            .from('avatars')
            .getPublicUrl(fileName);
          avatarUrl = publicUrl;
        }
      }

      // Then create/update the profile
      const { data, error } = await supabase
        .from('profiles')
        .upsert({
          id: authData.user.id,
          name: userData.full_name,
          full_name: userData.full_name,
          email: userData.email,
          role: userData.role,
          phone: userData.phone,
          avatar_url: avatarUrl,
          is_active: userData.is_active ?? true,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Create user error:', error);
      throw error;
    }
  },

  async updateUser(id: string, updates: {
    full_name?: string;
    role?: 'admin' | 'cashier' | 'kitchen';
    phone?: string;
    is_active?: boolean;
    avatar_file?: File | null;
  }): Promise<Profile> {
    try {
      let avatarUrl = undefined;
      
      // Handle avatar upload if provided
      if (updates.avatar_file) {
        const fileExt = updates.avatar_file.name.split('.').pop();
        const fileName = `${id}.${fileExt}`;
        
        const { error: uploadError } = await supabase.storage
          .from('avatars')
          .upload(fileName, updates.avatar_file, { upsert: true });

        if (!uploadError) {
          const { data: { publicUrl } } = supabase.storage
            .from('avatars')
            .getPublicUrl(fileName);
          avatarUrl = publicUrl;
        }
      }

      const updateData: any = {
        updated_at: new Date().toISOString(),
      };

      if (updates.full_name !== undefined) {
        updateData.name = updates.full_name;
        updateData.full_name = updates.full_name;
      }
      if (updates.role !== undefined) updateData.role = updates.role;
      if (updates.phone !== undefined) updateData.phone = updates.phone;
      if (updates.is_active !== undefined) updateData.is_active = updates.is_active;
      if (avatarUrl !== undefined) updateData.avatar_url = avatarUrl;

      const { data, error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  },

  async deleteUser(id: string): Promise<void> {
    try {
      // Delete the auth user (this will cascade to profile due to foreign key)
      const { error: authError } = await supabase.auth.admin.deleteUser(id);
      if (authError) throw authError;

      // Also delete from profiles table as backup
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', id);

      if (error && error.code !== 'PGRST116') { // Ignore "not found" errors
        throw error;
      }
    } catch (error) {
      console.error('Delete user error:', error);
      throw error;
    }
  },

  async updateUserStatus(id: string, status: 'active' | 'inactive'): Promise<Profile> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update({
          is_active: status === 'active',
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Update user status error:', error);
      throw error;
    }
  },

  async searchUsers(query: string, role?: string): Promise<Profile[]> {
    let queryBuilder = supabase
      .from('profiles')
      .select('*');

    if (query) {
      queryBuilder = queryBuilder.or(
        `full_name.ilike.%${query}%,name.ilike.%${query}%,email.ilike.%${query}%`
      );
    }

    if (role && role !== 'all') {
      queryBuilder = queryBuilder.eq('role', role);
    }

    const { data, error } = await queryBuilder
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getUserStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<string, number>;
  }> {
    const { data, error } = await supabase
      .from('profiles')
      .select('role, is_active');
    
    if (error) throw error;

    const stats = {
      total: data.length,
      active: data.filter(u => u.is_active !== false).length,
      inactive: data.filter(u => u.is_active === false).length,
      byRole: data.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    return stats;
  }
};
