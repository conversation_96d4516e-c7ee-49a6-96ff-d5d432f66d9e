'use client';

import { But<PERSON> } from '@/components/ui/button';
import { MinimalDatePicker } from '@/components/ui/MinimalDatePicker';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Filter, Download } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import toast from 'react-hot-toast';
import { DateRangePicker } from './DateRangePicker';

interface DashboardFiltersProps {
  // Date Range Props
  dateRange: DateRange;
  onDateRangeChange: (range: DateRange) => void;
  selectedPeriod: string;
  onPeriodSelect: (period: string) => void;

  // Filter Dialog Props
  isFilterOpen: boolean;
  onFilterOpenChange: (open: boolean) => void;

  // Export Props
  onExport?: (format: 'csv' | 'excel' | 'pdf') => void;
  isExporting?: boolean;

  // Customization
  showExport?: boolean;
  showCustomFilter?: boolean;
  className?: string;
}

export function DashboardFilters({
  dateRange,
  onDateRangeChange,
  selectedPeriod,
  onPeriodSelect,
  isFilterOpen,
  onFilterOpenChange,
  onExport,
  isExporting = false,
  showExport = true,
  showCustomFilter = true,
  className,
}: DashboardFiltersProps) {
  const handleApplyFilter = () => {
    onPeriodSelect('custom');
    onFilterOpenChange(false);
    toast.success('Filter applied successfully');
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Minimal Date Picker */}
      <MinimalDatePicker
        dateRange={dateRange}
        onDateRangeChange={onDateRangeChange}
        selectedPeriod={selectedPeriod}
        onPeriodSelect={onPeriodSelect}
      />

      {/* Custom Filter Dialog */}
      {showCustomFilter && (
        <Dialog open={isFilterOpen} onOpenChange={onFilterOpenChange}>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="border-slate-200 hover:bg-slate-50 text-slate-700 font-medium"
            >
              <Filter className="h-3.5 w-3.5 mr-1.5" />
              Filter
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md bg-white border border-slate-200">
            <DialogHeader>
              <DialogTitle>Filter Dashboard Data</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Date Range
                </label>
                <DateRangePicker
                  value={dateRange}
                  onChange={onDateRangeChange}
                  placeholder="Select date range"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => onFilterOpenChange(false)}
                >
                  Cancel
                </Button>
                <Button onClick={handleApplyFilter}>Apply Filter</Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Export Dropdown */}
      {showExport && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              className="bg-green-600 hover:bg-green-700 text-white"
              disabled={isExporting}
            >
              <Download className="h-4 w-4 mr-2" />
              {isExporting ? 'Exporting...' : 'Export Report'}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => onExport?.('csv')}>
              Export as CSV
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onExport?.('excel')}>
              Export as Excel
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onExport?.('pdf')}>
              Export as PDF
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}
