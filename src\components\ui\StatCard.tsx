import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LucideIcon, ArrowUpRight, ArrowDownRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatCardProps {
  title: string;
  value: number | string;
  change?: number;
  icon: LucideIcon;
  trend?: 'up' | 'down';
  formatValue?: 'currency' | 'number' | 'custom';
  className?: string;
}

export function StatCard({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  trend = 'up',
  formatValue = 'number',
  className 
}: StatCardProps) {
  const formatDisplayValue = () => {
    if (formatValue === 'currency' && typeof value === 'number') {
      return `Rp ${value.toLocaleString('id-ID')}`;
    }
    if (formatValue === 'number' && typeof value === 'number') {
      return value.toLocaleString('id-ID');
    }
    return value;
  };

  return (
    <Card className={cn(
      "relative overflow-hidden group hover:shadow-lg transition-all duration-300",
      className
    )}>
      <div className="absolute inset-0 bg-gradient-to-br from-white to-slate-50/50"></div>
      <CardHeader className="relative flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-slate-600">{title}</CardTitle>
        <div className="p-2 bg-lupizza-green-50 rounded-lg group-hover:bg-lupizza-green-100 transition-colors">
          <Icon className="h-4 w-4 text-lupizza-green-600" />
        </div>
      </CardHeader>
      <CardContent className="relative">
        <div className="text-2xl font-bold text-slate-900 mb-1">
          {formatDisplayValue()}
        </div>
        {change !== undefined && (
          <div className="flex items-center text-xs">
            {trend === 'up' ? (
              <ArrowUpRight className="h-3 w-3 text-lupizza-green-500 mr-1" />
            ) : (
              <ArrowDownRight className="h-3 w-3 text-lupizza-red-500 mr-1" />
            )}
            <span className={trend === 'up' ? 'text-lupizza-green-600' : 'text-lupizza-red-600'}>
              {change}% from last week
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}