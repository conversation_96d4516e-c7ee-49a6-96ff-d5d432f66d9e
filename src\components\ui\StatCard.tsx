"use client";

import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: number;
  format: 'currency' | 'number' | 'percentage';
  icon: LucideIcon;
  trend?: 'up' | 'down';
  change?: number;
  className?: string;
}

export function StatCard({ 
  title, 
  value, 
  format, 
  icon: Icon, 
  trend, 
  change, 
  className = '' 
}: StatCardProps) {
  const formatValue = (val: number, fmt: string) => {
    switch (fmt) {
      case 'currency':
        return new Intl.NumberFormat('id-ID', { 
          style: 'currency', 
          currency: 'IDR',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(val);
      case 'percentage':
        return `${val.toFixed(1)}%`;
      case 'number':
        return new Intl.NumberFormat('id-ID').format(val);
      default:
        return val.toString();
    }
  };

  return (
    <Card className={`hover:shadow-lupizza transition-shadow duration-200 ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-slate-600">{title}</p>
            <p className="text-2xl font-bold text-slate-900">
              {formatValue(value, format)}
            </p>
            {trend && change !== undefined && (
              <div className={`flex items-center space-x-1 text-sm ${
                trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {trend === 'up' ? (
                  <TrendingUp className="h-4 w-4" />
                ) : (
                  <TrendingDown className="h-4 w-4" />
                )}
                <span>{change}% from last period</span>
              </div>
            )}
          </div>
          <div className="p-3 bg-lupizza-green-100 rounded-full">
            <Icon className="h-6 w-6 text-lupizza-green-600" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
