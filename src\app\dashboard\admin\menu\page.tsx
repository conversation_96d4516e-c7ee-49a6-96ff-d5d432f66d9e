"use client";

import { useState } from 'react';
import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { MenuGrid } from '@/components/admin/menu/MenuGrid';
import { MenuForm } from '@/components/admin/menu/MenuForm';
import { MenuFilters } from '@/components/admin/menu/MenuFilters';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useMenu, useCategories } from '@/hooks/useMenu';
import { useDebounce } from 'use-debounce';
import { Plus, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Menu } from '@/types';

export default function MenuManagementPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedMenu, setSelectedMenu] = useState<Menu | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  
  const [debouncedSearch] = useDebounce(searchTerm, 300);
  
  const { data: menus = [], isLoading } = useMenu();
  const { data: categories = [] } = useCategories();

  const filteredMenus = menus.filter(menu => {
    const matchesSearch = menu.name.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
                         menu.description?.toLowerCase().includes(debouncedSearch.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || menu.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleEdit = (menu: Menu) => {
    setSelectedMenu(menu);
    setIsFormOpen(true);
  };

  const handleFormClose = () => {
    setSelectedMenu(null);
    setIsFormOpen(false);
  };

  return (
    <DashboardLayout requiredRole="admin">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Menu Management</h1>
            <p className="text-slate-600 mt-1">Manage your restaurant's menu items and categories</p>
          </div>
          
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button className="bg-lupizza-green-600 hover:bg-lupizza-green-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Menu Item
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {selectedMenu ? 'Edit Menu Item' : 'Add New Menu Item'}
                </DialogTitle>
              </DialogHeader>
              <MenuForm
                menu={selectedMenu}
                onSuccess={handleFormClose}
                onCancel={handleFormClose}
              />
            </DialogContent>
          </Dialog>
        </div>

        {/* Filters */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
            <Input
              placeholder="Search menu items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <MenuFilters
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
          />
        </div>

        {/* Menu Grid */}
        <MenuGrid
          menus={filteredMenus}
          isLoading={isLoading}
          onEdit={handleEdit}
        />
      </div>
    </DashboardLayout>
  );
}