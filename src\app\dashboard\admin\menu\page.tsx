'use client';

import { useState, useMemo } from 'react';
import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { MenuGrid } from '@/components/admin/menu/MenuGrid';
import { MenuForm } from '@/components/admin/menu/MenuForm';
import { MenuFilters } from '@/components/admin/menu/MenuFilters';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useMenu, useCategories } from '@/hooks/useMenu';
import { useDebounce } from 'use-debounce';
import {
  Plus,
  Search,
  Package,
  TrendingUp,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Menu } from '@/types';

export default function MenuManagementPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedMenu, setSelectedMenu] = useState<Menu | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [sortBy, setSortBy] = useState('name');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const [debouncedSearch] = useDebounce(searchTerm, 300);

  const { data: menus = [], isLoading } = useMenu();
  const { data: categories = [] } = useCategories();

  const filteredAndSortedMenus = useMemo(() => {
    let filtered = menus.filter((menu) => {
      const matchesSearch =
        menu.name.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
        menu.description?.toLowerCase().includes(debouncedSearch.toLowerCase());
      const matchesCategory =
        selectedCategory === 'all' || menu.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'name_desc':
          return b.name.localeCompare(a.name);
        case 'price':
          return a.price - b.price;
        case 'price_desc':
          return b.price - a.price;
        case 'category':
          return a.category.localeCompare(b.category);
        case 'created_at':
          return (
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }, [menus, debouncedSearch, selectedCategory, sortBy]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalMenus = menus.length;
    const availableMenus = menus.filter((menu) => menu.available).length;
    const unavailableMenus = totalMenus - availableMenus;
    const categoriesCount = new Set(menus.map((menu) => menu.category)).size;

    return {
      total: totalMenus,
      available: availableMenus,
      unavailable: unavailableMenus,
      categories: categoriesCount,
    };
  }, [menus]);

  const handleEdit = (menu: Menu) => {
    setSelectedMenu(menu);
    setIsFormOpen(true);
  };

  const handleFormClose = () => {
    setSelectedMenu(null);
    setIsFormOpen(false);
  };

  return (
    <DashboardLayout requiredRole="admin">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">
              Menu Management
            </h1>
            <p className="text-slate-600 mt-1">
              Manage your restaurant's menu items and categories
            </p>
          </div>

          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button className="bg-lupizza-green-600 hover:bg-lupizza-green-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Menu Item
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {selectedMenu ? 'Edit Menu Item' : 'Add New Menu Item'}
                </DialogTitle>
              </DialogHeader>
              <MenuForm
                menu={selectedMenu}
                onSuccess={handleFormClose}
                onCancel={handleFormClose}
              />
            </DialogContent>
          </Dialog>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="border-slate-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Total Items
              </CardTitle>
              <Package className="h-4 w-4 text-slate-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">
                {stats.total}
              </div>
              <p className="text-xs text-slate-500 mt-1">
                {filteredAndSortedMenus.length !== stats.total &&
                  `${filteredAndSortedMenus.length} filtered`}
              </p>
            </CardContent>
          </Card>

          <Card className="border-slate-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Available
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats.available}
              </div>
              <p className="text-xs text-slate-500 mt-1">
                {stats.total > 0
                  ? `${Math.round((stats.available / stats.total) * 100)}% of total`
                  : '0% of total'}
              </p>
            </CardContent>
          </Card>

          <Card className="border-slate-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Unavailable
              </CardTitle>
              <AlertCircle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {stats.unavailable}
              </div>
              <p className="text-xs text-slate-500 mt-1">
                {stats.total > 0
                  ? `${Math.round((stats.unavailable / stats.total) * 100)}% of total`
                  : '0% of total'}
              </p>
            </CardContent>
          </Card>

          <Card className="border-slate-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Categories
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats.categories}
              </div>
              <p className="text-xs text-slate-500 mt-1">Active categories</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
            <Input
              placeholder="Search menu items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <MenuFilters
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            sortBy={sortBy}
            onSortChange={setSortBy}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />
        </div>

        {/* Menu Grid */}
        <MenuGrid
          menus={filteredAndSortedMenus}
          isLoading={isLoading}
          onEdit={handleEdit}
          viewMode={viewMode}
        />
      </div>
    </DashboardLayout>
  );
}
