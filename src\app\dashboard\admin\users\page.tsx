"use client";

import { useState } from 'react';
import { DashboardLayout } from '@/components/shared/DashboardLayout';
import { UserTable } from '@/components/admin/users/UserTable';
import { UserForm } from '@/components/admin/users/UserForm';
import { UserFilters } from '@/components/admin/users/UserFilters';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useUsers } from '@/hooks/useUsers';
import { useDebounce } from 'use-debounce';
import { Plus, Search, Users } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Profile } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function UserManagementPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<Profile | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  
  const [debouncedSearch] = useDebounce(searchTerm, 300);
  
  const { data: users = [], isLoading } = useUsers();

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.full_name?.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
                         user.email?.toLowerCase().includes(debouncedSearch.toLowerCase());
    const matchesRole = selectedRole === 'all' || user.role === selectedRole;
    return matchesSearch && matchesRole;
  });

  const userStats = {
    total: users.length,
    admin: users.filter(u => u.role === 'admin').length,
    cashier: users.filter(u => u.role === 'cashier').length,
    kitchen: users.filter(u => u.role === 'kitchen').length,
  };

  const handleEdit = (user: Profile) => {
    setSelectedUser(user);
    setIsFormOpen(true);
  };

  const handleFormClose = () => {
    setSelectedUser(null);
    setIsFormOpen(false);
  };

  return (
    <DashboardLayout requiredRole="admin">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">User Management</h1>
            <p className="text-slate-600 mt-1">Manage staff accounts and permissions</p>
          </div>
          
          <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
            <DialogTrigger asChild>
              <Button className="bg-lupizza-green-600 hover:bg-lupizza-green-700">
                <Plus className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {selectedUser ? 'Edit User' : 'Add New User'}
                </DialogTitle>
              </DialogHeader>
              <UserForm
                user={selectedUser}
                onSuccess={handleFormClose}
                onCancel={handleFormClose}
              />
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Total Users</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-900">{userStats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Admins</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-lupizza-red-600">{userStats.admin}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Cashiers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-lupizza-green-600">{userStats.cashier}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">Kitchen Staff</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-lupizza-cream-600">{userStats.kitchen}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <UserFilters
            selectedRole={selectedRole}
            onRoleChange={setSelectedRole}
          />
        </div>

        {/* User Table */}
        <UserTable
          users={filteredUsers}
          isLoading={isLoading}
          onEdit={handleEdit}
        />
      </div>
    </DashboardLayout>
  );
}